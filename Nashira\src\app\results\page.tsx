"use client"

import { useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"
import ScrapingResults, { type ScrapedResult } from "~/components/results/ScrapingResults"
import { But<PERSON> } from "~/components/ui/button"
import Link from "next/link"

export default function ResultsPage() {
  const searchParams = useSearchParams()
  const [results, setResults] = useState<ScrapedResult[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const type = searchParams.get("type") || "data"
  const location = searchParams.get("location") || ""
  const category = searchParams.get("category") || ""
  const sessionId = searchParams.get("session_id") || ""

  useEffect(() => {
    const fetchResults = async () => {
      if (!sessionId) {
        setError("No session ID provided")
        setLoading(false)
        return
      }

      setLoading(true)

      try {
        const response = await fetch(`http://127.0.0.1:8000/api/wine/session/${sessionId}/`)

        if (!response.ok) {
          throw new Error(`Failed to fetch results: ${response.status}`)
        }

        const data = await response.json()

        // Transform Django results to match the expected format
        const transformedResults: ScrapedResult[] = data.results.map((result: any) => ({
          name: result.name || "N/A",
          address: result.address || "N/A",
          phone: result.phone || "N/A",
          url: result.url || "",
          category: result.category || category,
          social_links: result.social_links || "N/A",
          is_shopify: result.is_shopify || false,
          is_active: result.is_active || false,
          status: result.status || "Unknown"
        }))

        setResults(transformedResults)
        setLoading(false)
      } catch (err) {
        console.error("Error fetching results:", err)
        setError(err instanceof Error ? err.message : "Failed to fetch results")
        setLoading(false)
      }
    }

    fetchResults()
  }, [sessionId, location, category])

  const getTitle = () => {
    if (type === "wine") {
      return `Wine Data Results for ${category}`
    }
    return `Business Data Results for ${category} in ${location}`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[hsl(280,100%,70%)] to-[hsl(240,100%,70%)] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto mb-4"></div>
          <h2 className="text-2xl font-bold text-white mb-2">Scraping Data...</h2>
          <p className="text-white/70">Please wait while we gather the information</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[hsl(280,100%,70%)] to-[hsl(240,100%,70%)] flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4">Error</h2>
          <p className="text-white/70 mb-6">{error}</p>
          <Link href="/">
            <Button className="bg-white/20 hover:bg-white/30 text-white">
              Go Back Home
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[hsl(280,100%,70%)] to-[hsl(240,100%,70%)]">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link href="/">
            <Button variant="outline" className="mb-4 text-white border-white/20 hover:bg-white/10">
              ← Back to Home
            </Button>
          </Link>
        </div>

        {/* Results */}
        <ScrapingResults data={results} title={getTitle()} />
      </div>
    </div>
  )
}
